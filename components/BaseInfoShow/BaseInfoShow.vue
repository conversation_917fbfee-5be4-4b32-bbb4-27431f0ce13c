<template>
    <div class="BaseInfoShow w100">
        <view class="info-show info-show-one w100 df flr jc-sb alc" :style="[boxStyleCon]" v-if="type === '1'">
            <block v-for="(item,index) in data" :key="index">
                <view class="info-show-item df flc jc-fs alc" @click="clickInfo(item)">
                    <view class="item-data dfc">
                        <text :style="[dataTextStyleCon]">{{item.value}}</text>
                    </view>
                    <view class="item-text dfc">
                        <text :style="[textStyleCon]">{{item.label}}</text>
                    </view>
                </view>
            </block>
        </view>
        <view class="info-show info-show-two w100" v-if="type === '2'">
            <view class="info-show-box w100 df flc jc-fs alc bc1">
                <view class="info-title w100 df flr jc-fs alc">
                    <text class="fwb fc5">{{ title }}</text>
                </view>
                <view class="info-list w100 df flr jc-sb alc">
                    <block v-for="(item,index) in data" :key="index">
                        <view class="info-item df flc jc-fs alc" @click="clickInfo(item)">
                            <view class="item-pic dfc">
                                <view class="pic-num dfc" v-show="item.isShow" :class="item.value < 10?'pic-num1':'pic-num2'">
                                    <text class="fc1">{{item.value}}</text>
                                </view>
                                <view class="pic-con dfc">
                                    <image :src="`../../static/${item.imageUrl}`"></image>
                                </view>
                            </view>
                            <view class="item-text dfc">
                                <text>{{item.label}}</text>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
        </view>
        <view class="info-show info-show-thr w100" v-if="type === '3'">
            <view class="info-show-box w100 df flc jc-fs alc bc1">
                <view class="health-title w100 df flr jc-fs alc">
                    <text class="fwb fc2">{{ title }}</text>
                </view>
                <uni-swiper-dot class="uni-swiper-dot-box w100" :info="info" :current="current" mode="round" :dots-styles="dotsStyles" field="content">
                    <swiper class="swiper-box w100" :class="current === 0?'gaodu1': 'gaodu2'" @change="change">
                        <swiper-item class="swiper-item" v-for="(item, index) in info" :key="index">
                            <block v-for="(ite,ind) in item['data']" :key="ind">
                                <view class="item-con w100" @click="clickInfo(ite)" v-if="ite.isShow !== false">
                                    <view class="item-pic dfc">
                                        <image v-if="ite.imageUrl" :style="{ width: `${imgSize}`, height: `${imgSize}` }" :src="isHaveHttp(ite.imageUrl)?ite.imageUrl:`../../static/${ite.imageUrl}`" mode="aspectFill"></image>
                                        <text v-if="ite.icon" class="iconfont" :class="ite.icon"></text>
                                    </view>
                                    <view class="item-text dfc">
                                        <text>{{ ite['label'] }}</text>
                                    </view>
                                </view>
                            </block>
                        </swiper-item>
                    </swiper>
                </uni-swiper-dot>
            </view>
        </view>
        <view class="info-show info-show-thr w100" v-if="type === '4'">
            <view class="info-show-box w100 df flc jc-fs alc bc1">
                <view class="health-title w100 df flr jc-fs alc">
                    <text class="fwb fc2">{{ title }}</text>
                </view>
                <uni-swiper-dot class="uni-swiper-dot-box w100" :info="info" :current="current" mode="round" :dots-styles="dotsStyles" field="content">
                    <swiper class="swiper-box w100" :class="current === 0?'gaodu1': 'gaodu2'" @change="change">
                        <swiper-item class="swiper-item" v-for="(item, index) in info" :key="index" style="grid-template-columns: repeat(4, 1fr)">
                            <block v-for="(ite,ind) in item['data']" :key="ind">
                                <view class="item-con w100" @click="clickInfo(ite)" v-if="ite.isShow !== false">
                                    <view class="item-pic dfc">
                                        <image v-if="ite.imageUrl" :style="{ width: `${imgSize}`, height: `${imgSize}` }" :src="isHaveHttp(ite.imageUrl)?ite.imageUrl:`../../static/${ite.imageUrl}`" mode="aspectFill"></image>
                                    </view>
                                    <view class="item-text dfc">
                                        <text>{{ ite['label'] }}</text>
                                    </view>
                                </view>
                            </block>
                        </swiper-item>
                    </swiper>
                </uni-swiper-dot>
            </view>
        </view>
    </div>
</template>

<script name="BaseInfoShow">
export default {
    props: {
        type: {
            type: String,
            default: '1'
        },
        title: {
            type: String,
            default: ''
        },
        data: {
            type: Array,
            required: true,
            default: ()=>{
                return []
            }
        },
        imgSize: {
            type: String,
            default: ''
        },
        boxStyle: {
            type: Object,
            default: () => {
                return {}
            }
        },
        dataTextStyle: {
            type: Object,
            default: () => {
                return {}
            }
        },
        textStyle: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            current: 0,
            dotsStyles: {
                width: 10,
                backgroundColor: 'rgba(255, 90, 95,0.3)',
                border: '1px rgba(255, 90, 95,0.3) solid',
                color: '#ffffff',
                selectedBackgroundColor: 'rgba(255, 90, 95,0.9)',
                selectedBorder: '1px rgba(255, 90, 95,0.9) solid',
            }
        }
    },
    mounted() {

    },
    computed: {
        boxStyleCon() {
            let that = this;
            let styleObj = {}
            if(that.type === '1') {
                styleObj = {
                    padding: '0 65rpx 0 70rpx'
                }
            }
            return { ...that.boxStyle, ...styleObj }
        },
        dataTextStyleCon() {
            let that = this;
            let styleObj = {}
            if(that.type === '1') {
                styleObj = {
                    fontSize: '32rpx',
                    fontWeight: 'bold',
                    color: '#FFFFFF'
                }
            }
            return { ...that.dataTextStyle, ...styleObj }
        },
        textStyleCon() {
            let that = this;
            let styleObj = {}
            if(that.type === '1') {
                styleObj = {
                    fontSize: '28rpx',
                    fontWeight: '500',
                    color: '#FFFFFF'
                }
            }
            return { ...that.textStyle, ...styleObj }
        },
        info() {
            let that = this
            let arr1 = []
            let arr2 = []
            let arr3 = []
            if(Array.isArray(that.data) && that.data.length > 0) {
                that.data.map((item,index)=>{
                    if(index > 4) {
                        arr2.push(item)
                    } else {
                        arr1.push(item)
                    }
                })
                if(arr2.length > 10) {
                    arr2.map((item,index)=>{
                        if(index < 8) {
                            arr3.push(item)
                        }
                    })
                    arr3.push({
                        label: "更多",
                        imageUrl: "http://video.xiyuns.cn/1/material/168f3fd4-fa90-4efd-bd11-171418c40fca.png",
                        value: ""
                    })
                } else {
                    arr2.map((item,index)=>{
                        arr3.push(item)
                    })
                }
            }
            if(that.type === '4') {
                return [{data: arr1}]
            }
            return [{data: arr1}, {data: arr3}]
        }
    },
    methods: {
        clickInfo(data) {
            this.$emit('clickInfo', data)
        },
        change(e) {
            this.current = e.detail.current
        },
        isHaveHttp(val) {
            return val && val.indexOf('http') !== -1;
        }
    }
}
</script>

<style lang="scss" scoped>
.BaseInfoShow {
    .info-show-one {
        .info-show-item {
            .item-data {
                padding-bottom: 19rpx;
            }
        }
    }
    .info-show-two {
        padding: 0 20rpx;
        .info-show-box {
            border-radius: 20rpx;
            padding: 30rpx;
            .info-title {
                padding-bottom: 50rpx;
                text {
                    font-size: 34rpx;
                }
            }
            .info-list {
                .info-item {
                    .item-pic {
                        position: relative;
                        .pic-con {
                            image {
                                width: 45rpx;
                                height: 45rpx;
                            }
                        }
                        .pic-num {
                            position: absolute;
                            top: -20rpx;
                            background-color: #FF5651;
                            border: 4rpx solid #FFFFFF;
                            text {
                                font-size: 22rpx;
                                font-weight: 500;
                            }
                        }
                        .pic-num1 {
                            width: 34rpx;
                            height: 34rpx;
                            right: -25rpx;
                            border-radius: 50%;
                        }
                        .pic-num2 {
                            width: 42rpx;
                            height: 34rpx;
                            right: -30rpx;
                            border-radius: 15rpx;
                        }
                    }
                }
            }
        }
    }
    .info-show-thr {
        padding: 0 20rpx;
        .info-show-box {
            border-radius: 20rpx;
            padding: 30rpx;
            .health-title {
                margin-bottom: 33rpx;

                text {
                    font-size: 32rpx;
                }
            }
            .uni-swiper-dot-box {
                .swiper-box {
                    transition: height 0.5s;
                    .swiper-item {
                        display: grid;
                        grid-template-columns: repeat(5, 1fr);

                        .item-con {
                            .item-pic {
                                margin-bottom: 17rpx;
                                height: 50rpx;
                                .iconfont {
                                    font-size: 55rpx;
                                    // transform: translateY(-2rpx);
                                    display: block;
                                    width: 55rpx;
                                    height: 55rpx;
                                }
                                image {
                                    border-radius: 20rpx;
                                }
                            }
                            .item-text {
                                text {
                                    font-size: 24rpx;
                                    color: #242424;
                                    font-weight: 500;
                                }
                            }
                        }
                    }
                }
                .gaodu1 {
                    height: 180rpx;
                }
                .gaodu2 {
                    height: 360rpx;
                }
            }
        }
    }
}
</style>